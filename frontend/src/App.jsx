import { useState } from 'react'
import ScrapingForm from './components/ScrapingForm'
import SearchForm from './components/SearchForm'
import ResultDisplay from './components/ResultDisplay'
import SearchResultDisplay from './components/SearchResultDisplay'
import BulkResultDisplay from './components/BulkResultDisplay'
import './App.css'

function App() {
  const [scrapingResult, setScrapingResult] = useState(null)
  const [isLoading, setIsLoading] = useState(false)
  const [activeTab, setActiveTab] = useState('scrape')
  const [abortController, setAbortController] = useState(null)
  const [resultType, setResultType] = useState('scrape') // 'scrape', 'search', or 'bulk'
  const [searchFormElements, setSearchFormElements] = useState(['title', 'description', 'images', 'price']) // Elements from search form
  const [searchFormCustomElements, setSearchFormCustomElements] = useState('')

  const handleScrapingResult = (result) => {
    setScrapingResult(result)
    setIsLoading(false)
    setAbortController(null)
    setResultType('scrape')
  }

  const handleSearchResult = (result) => {
    setScrapingResult(result)
    setIsLoading(false)
    setAbortController(null)
    setResultType('search')
  }

  const handleScrapingStart = (controller) => {
    setIsLoading(true)
    setScrapingResult(null)
    setAbortController(controller)
  }

  const handleScrapingStop = () => {
    if (abortController) {
      abortController.abort()
      setAbortController(null)
    }
    setIsLoading(false)
    setScrapingResult({
      success: false,
      error: 'Operation was cancelled by user',
      message: 'The operation was stopped before completion.'
    })
  }

  const handleScrapeSelectedLinks = async (links) => {
    if (!links || links.length === 0) {
      alert('No links selected for scraping')
      return
    }

    // Create AbortController for bulk scraping
    const controller = new AbortController()
    setIsLoading(true)
    setScrapingResult(null)
    setAbortController(controller)
    setResultType('bulk')

    try {
      console.log('Starting bulk scraping for', links.length, 'links')

      // Prepare URLs for bulk scraping
      const urls = links.map(link => ({
        url: link.url,
        title: link.title || link.text,
        domain: link.domain
      }))

      // Combine selected elements with custom elements from search form
      const allElements = [...searchFormElements]
      if (searchFormCustomElements.trim()) {
        const customElementsList = searchFormCustomElements.split(',').map(el => el.trim()).filter(el => el)
        allElements.push(...customElementsList)
      }

      const response = await fetch('http://localhost:3001/api/scrape/bulk', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          urls,
          scraper: 'stealth-playwright', // Default scraper for bulk operations
          elements: allElements.length > 0 ? allElements : ['title', 'description', 'images', 'price'], // Use search form elements or defaults
          maxConcurrent: 2, // Limit concurrent requests
          delayBetween: 2000 // 2 second delay between batches
        }),
        signal: controller.signal
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()

      setScrapingResult(result)
      setIsLoading(false)
      setAbortController(null)

      console.log('Bulk scraping completed:', result)

    } catch (error) {
      console.error('Bulk scraping error:', error)

      // Check if the error was due to cancellation
      if (error.name === 'AbortError') {
        return // Don't set result, let the stop handler deal with it
      }

      setScrapingResult({
        success: false,
        error: 'Bulk scraping failed',
        message: error.message
      })
      setIsLoading(false)
      setAbortController(null)
    }
  }

  const tabs = [
    { id: 'scrape', label: 'Single URL', path: '/scrape' },
    { id: 'crawl', label: 'Crawl', path: '/crawl', disabled: true },
    { id: 'map', label: 'Map', path: '/map', disabled: true },
    { id: 'search', label: 'Search', path: '/search', badge: 'NEW', disabled: false }
  ]

  return (
    <div className="app">
      <header className="app-header">
        <div className="header-content">
          <div className="header-title">
            <h1>Scrape Playground</h1>
            <p>Check out multiple libraries - all in one place</p>
          </div>
        </div>

        <nav className="tab-navigation">
          {tabs.map(tab => (
            <button
              key={tab.id}
              className={`tab ${activeTab === tab.id ? 'active' : ''} ${tab.disabled ? 'disabled' : ''}`}
              onClick={() => !tab.disabled && setActiveTab(tab.id)}
              disabled={tab.disabled}
            >
              {tab.label}
              {tab.path && <span className="tab-path">{tab.path}</span>}
              {tab.badge && <span className="tab-badge">{tab.badge}</span>}
            </button>
          ))}
        </nav>
      </header>

      <main className="app-main">
        {activeTab === 'scrape' && (
          <>
            <ScrapingForm
              onResult={handleScrapingResult}
              onStart={handleScrapingStart}
              onStop={handleScrapingStop}
              isLoading={isLoading}
            />

            {isLoading && (
              <div className="loading">
                <div className="spinner"></div>
                <p>{resultType === 'bulk' ? 'Bulk scraping in progress...' : 'Scraping in progress...'}</p>
                <p className="loading-hint">Click the Stop button to cancel</p>
              </div>
            )}

            {scrapingResult && resultType === 'scrape' && (
              <ResultDisplay result={scrapingResult} />
            )}

            {scrapingResult && resultType === 'bulk' && (
              <BulkResultDisplay result={scrapingResult} />
            )}
          </>
        )}

        {activeTab === 'search' && (
          <>
            <SearchForm
              onResult={handleSearchResult}
              onStart={handleScrapingStart}
              onStop={handleScrapingStop}
              isLoading={isLoading}
              onElementsChange={setSearchFormElements}
              onCustomElementsChange={setSearchFormCustomElements}
              elements={searchFormElements}
              customElements={searchFormCustomElements}
            />

            {isLoading && (
              <div className="loading">
                <div className="spinner"></div>
                <p>Extracting product links...</p>
                <p className="loading-hint">Click the Stop button to cancel</p>
              </div>
            )}

            {scrapingResult && resultType === 'search' && (
              <SearchResultDisplay
                result={scrapingResult}
                onScrapeLink={handleScrapeSelectedLinks}
              />
            )}

            {scrapingResult && resultType === 'scrape' && (
              <ResultDisplay result={scrapingResult} />
            )}

            {scrapingResult && resultType === 'bulk' && (
              <BulkResultDisplay result={scrapingResult} />
            )}
          </>
        )}
      </main>
    </div>
  )
}

export default App
