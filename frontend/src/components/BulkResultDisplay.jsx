import { useState, useEffect } from 'react'

// Import the same components from ResultDisplay
const ImageGallery = ({ images, onImagesChange, showDeleteButtons = true }) => {
  const [imageErrors, setImageErrors] = useState({})
  const [imageLoading, setImageLoading] = useState({})

  const handleImageError = (index) => {
    setImageErrors(prev => ({ ...prev, [index]: true }))
    setImageLoading(prev => ({ ...prev, [index]: false }))
  }

  const handleImageLoad = (index) => {
    setImageLoading(prev => ({ ...prev, [index]: false }))
  }

  const handleImageLoadStart = (index) => {
    setImageLoading(prev => ({ ...prev, [index]: true }))
  }

  const deleteImage = (indexToDelete) => {
    const newImages = images.filter((_, index) => index !== indexToDelete)
    onImagesChange(newImages)
  }

  if (!images || images.length === 0) {
    return <div className="no-images">No images found</div>
  }

  return (
    <div className="image-gallery">
      <h4>🖼️ Images ({images.length})</h4>
      <div className="image-grid">
        {images.map((imageUrl, index) => (
          <div key={index} className="image-item">
            {showDeleteButtons && (
              <button
                className="delete-image-btn"
                onClick={() => deleteImage(index)}
                title="Delete image"
              >
                ✕
              </button>
            )}
            {imageLoading[index] && (
              <div className="image-loading">Loading...</div>
            )}
            {imageErrors[index] ? (
              <div className="image-error">
                <span>❌ Failed to load</span>
                <a href={imageUrl} target="_blank" rel="noopener noreferrer" className="image-url">
                  {imageUrl.length > 30 ? imageUrl.substring(0, 30) + '...' : imageUrl}
                </a>
              </div>
            ) : (
              <img
                src={imageUrl}
                alt={`Scraped image ${index + 1}`}
                onError={() => handleImageError(index)}
                onLoad={() => handleImageLoad(index)}
                onLoadStart={() => handleImageLoadStart(index)}
                loading="lazy"
              />
            )}
          </div>
        ))}
      </div>
    </div>
  )
}

const EditableField = ({ label, value, onChange, onDelete, type = 'text', isArray = false }) => {
  const [isEditing, setIsEditing] = useState(false)
  const [editValue, setEditValue] = useState(value)

  useEffect(() => {
    setEditValue(value)
  }, [value])

  const handleSave = () => {
    if (isArray) {
      const arrayValue = typeof editValue === 'string'
        ? editValue.split(',').map(item => item.trim()).filter(item => item)
        : editValue
      onChange(arrayValue)
    } else {
      onChange(editValue)
    }
    setIsEditing(false)
  }

  const handleCancel = () => {
    setEditValue(value)
    setIsEditing(false)
  }

  const displayValue = isArray && Array.isArray(value) ? value.join(', ') : value

  return (
    <div className="editable-field">
      <div className="field-header">
        <label className="field-label">{label}:</label>
        {onDelete && !isEditing && (
          <button
            onClick={(e) => {
              e.stopPropagation()
              onDelete()
            }}
            className="delete-field-btn"
            title={`Delete ${label}`}
          >
            🗑️
          </button>
        )}
      </div>
      {isEditing ? (
        <div className="edit-mode">
          {type === 'textarea' ? (
            <textarea
              value={isArray ? (Array.isArray(editValue) ? editValue.join(', ') : editValue) : editValue}
              onChange={(e) => setEditValue(e.target.value)}
              rows={3}
              className="edit-input"
            />
          ) : (
            <input
              type={type}
              value={isArray ? (Array.isArray(editValue) ? editValue.join(', ') : editValue) : editValue}
              onChange={(e) => setEditValue(e.target.value)}
              className="edit-input"
            />
          )}
          <div className="edit-actions">
            <button onClick={handleSave} className="save-btn">✓</button>
            <button onClick={handleCancel} className="cancel-btn">✕</button>
          </div>
        </div>
      ) : (
        <div className="view-mode" onClick={() => setIsEditing(true)}>
          <span className="field-value">
            {displayValue || <em>No value</em>}
          </span>
          <span className="edit-hint">✏️</span>
        </div>
      )}
    </div>
  )
}

const BulkResultDisplay = ({ result }) => {
  const [viewMode, setViewMode] = useState('summary')
  const [selectedResult, setSelectedResult] = useState(null)
  const [expandedResults, setExpandedResults] = useState(new Set())
  const [editableResults, setEditableResults] = useState([])

  // Initialize editable results when result changes
  useEffect(() => {
    if (result && result.data && result.data.results) {
      setEditableResults(result.data.results.map(item => ({ ...item })))
    }
  }, [result])

  // Update a specific field in a specific result
  const updateResultField = (resultIndex, fieldName, newValue) => {
    setEditableResults(prev =>
      prev.map((item, index) =>
        index === resultIndex
          ? { ...item, data: { ...item.data, data: { ...item.data?.data, [fieldName]: newValue } } }
          : item
      )
    )
  }

  // Delete a field from a specific result
  const deleteResultField = (resultIndex, fieldName) => {
    const confirmDelete = window.confirm(`Are you sure you want to delete the "${fieldName}" field? This action cannot be undone.`)
    if (confirmDelete) {
      setEditableResults(prev =>
        prev.map((item, index) => {
          if (index === resultIndex && item.data?.data) {
            const newData = { ...item.data.data }
            delete newData[fieldName]
            return { ...item, data: { ...item.data, data: newData } }
          }
          return item
        })
      )
    }
  }

  // Get the current result with editable data
  const getCurrentResult = () => {
    if (!result || editableResults.length === 0) return result
    return {
      ...result,
      data: {
        ...result.data,
        results: editableResults
      }
    }
  }

  const downloadJson = () => {
    const currentResult = getCurrentResult()
    const dataStr = JSON.stringify(currentResult, null, 2)
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr)
    const exportFileDefaultName = `bulk-scraping-results-${new Date().toISOString().split('T')[0]}.json`

    const linkElement = document.createElement('a')
    linkElement.setAttribute('href', dataUri)
    linkElement.setAttribute('download', exportFileDefaultName)
    linkElement.click()
  }

  const copyToClipboard = () => {
    const currentResult = getCurrentResult()
    navigator.clipboard.writeText(JSON.stringify(currentResult, null, 2))
      .then(() => alert('JSON copied to clipboard!'))
      .catch(err => console.error('Failed to copy: ', err))
  }

  const toggleResultExpansion = (index) => {
    const newExpanded = new Set(expandedResults)
    if (newExpanded.has(index)) {
      newExpanded.delete(index)
    } else {
      newExpanded.add(index)
    }
    setExpandedResults(newExpanded)
  }

  const formatDuration = (ms) => {
    const seconds = Math.floor(ms / 1000)
    const minutes = Math.floor(seconds / 60)
    if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`
    }
    return `${seconds}s`
  }

  if (!result) return null

  const { data } = result
  const { results = [], summary = {} } = data || {}

  return (
    <div className="result-display">
      <div className="result-header">
        <h2>🚀 Bulk Scraping Results</h2>
        <div className="result-actions">
          <div className="view-toggle">
            <button 
              className={viewMode === 'summary' ? 'active' : ''}
              onClick={() => setViewMode('summary')}
            >
              Summary
            </button>
            <button 
              className={viewMode === 'detailed' ? 'active' : ''}
              onClick={() => setViewMode('detailed')}
            >
              Detailed
            </button>
            <button 
              className={viewMode === 'raw' ? 'active' : ''}
              onClick={() => setViewMode('raw')}
            >
              Raw JSON
            </button>
          </div>
          <button onClick={copyToClipboard} className="action-btn">
            📋 Copy
          </button>
          <button onClick={downloadJson} className="action-btn">
            💾 Download
          </button>
        </div>
      </div>

      <div className="result-content">
        {result.success === false ? (
          <div className="error-result">
            <h3>❌ Bulk Scraping Failed</h3>
            <p><strong>Error:</strong> {result.error}</p>
            {result.message && <p><strong>Details:</strong> {result.message}</p>}
          </div>
        ) : (
          <>
            {/* Summary Statistics */}
            <div className="result-meta">
              <div className="meta-item">
                <strong>Total URLs:</strong> {summary.total || 0}
              </div>
              <div className="meta-item">
                <strong>Successful:</strong> <span style={{color: '#10b981'}}>{summary.successful || 0}</span>
              </div>
              <div className="meta-item">
                <strong>Failed:</strong> <span style={{color: '#ef4444'}}>{summary.failed || 0}</span>
              </div>
              <div className="meta-item">
                <strong>Duration:</strong> {summary.duration ? formatDuration(summary.duration) : 'N/A'}
              </div>
              <div className="meta-item">
                <strong>Scraper:</strong> {summary.scraper || 'N/A'}
              </div>
              <div className="meta-item">
                <strong>Completed:</strong> {summary.completedAt ? new Date(summary.completedAt).toLocaleString() : 'N/A'}
              </div>
            </div>

            {viewMode === 'summary' && (
              <div className="bulk-summary">
                <h3>📊 Results Overview</h3>
                <div className="bulk-results-grid">
                  {results.map((item, index) => (
                    <div 
                      key={index} 
                      className={`bulk-result-card ${item.success ? 'success' : 'failed'}`}
                      onClick={() => setSelectedResult(item)}
                    >
                      <div className="result-status">
                        {item.success ? '✅' : '❌'}
                      </div>
                      <div className="result-info">
                        <div className="result-title">
                          {item.title || item.data?.data?.title || 'No title'}
                        </div>
                        <div className="result-domain">
                          {item.domain || new URL(item.url).hostname}
                        </div>
                        <div className="result-url">
                          {item.url.length > 50 ? item.url.substring(0, 50) + '...' : item.url}
                        </div>
                        {!item.success && (
                          <div className="result-error">
                            {item.error || 'Unknown error'}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {viewMode === 'detailed' && (
              <div className="bulk-detailed">
                <h3>📋 Detailed Results</h3>
                <div className="detailed-results">
                  {results.map((item, index) => (
                    <div key={index} className="detailed-result-item">
                      <div 
                        className="detailed-result-header"
                        onClick={() => toggleResultExpansion(index)}
                      >
                        <div className="result-summary">
                          <span className={`status-icon ${item.success ? 'success' : 'failed'}`}>
                            {item.success ? '✅' : '❌'}
                          </span>
                          <span className="result-title">
                            {item.title || item.data?.data?.title || 'No title'}
                          </span>
                          <span className="result-domain">
                            ({item.domain || new URL(item.url).hostname})
                          </span>
                        </div>
                        <span className="expand-icon">
                          {expandedResults.has(index) ? '▼' : '▶'}
                        </span>
                      </div>
                      
                      {expandedResults.has(index) && (
                        <div className="detailed-result-content">
                          <div className="result-url-full">
                            <strong>URL:</strong> <a href={item.url} target="_blank" rel="noopener noreferrer">{item.url}</a>
                          </div>
                          
                          {item.success ? (
                            <div className="scraped-data">
                              <h4>📝 Editable Scraped Data:</h4>
                              <div className="editable-data-layout">
                                <div className="editable-fields">
                                  {editableResults[index]?.data?.data && Object.entries(editableResults[index].data.data).map(([key, value]) => {
                                    if (key === 'images') {
                                      return null // Handle images separately
                                    }

                                    const isArray = Array.isArray(value)
                                    const fieldType = key.includes('description') || key.includes('content') ? 'textarea' : 'text'

                                    return (
                                      <EditableField
                                        key={key}
                                        label={key.charAt(0).toUpperCase() + key.slice(1)}
                                        value={value}
                                        onChange={(newValue) => updateResultField(index, key, newValue)}
                                        onDelete={() => deleteResultField(index, key)}
                                        type={fieldType}
                                        isArray={isArray}
                                      />
                                    )
                                  })}
                                </div>

                                {editableResults[index]?.data?.data?.images && editableResults[index].data.data.images.length > 0 && (
                                  <div className="images-section">
                                    <ImageGallery
                                      images={editableResults[index].data.data.images}
                                      onImagesChange={(newImages) => updateResultField(index, 'images', newImages)}
                                      showDeleteButtons={true}
                                    />
                                  </div>
                                )}
                              </div>
                            </div>
                          ) : (
                            <div className="error-details">
                              <h4>Error Details:</h4>
                              <p><strong>Error:</strong> {item.error}</p>
                              {item.message && <p><strong>Message:</strong> {item.message}</p>}
                            </div>
                          )}
                          
                          <div className="result-timestamp">
                            <small>Scraped at: {new Date(item.scrapedAt).toLocaleString()}</small>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {viewMode === 'raw' && (
              <pre className="raw-json">
                {JSON.stringify(getCurrentResult(), null, 2)}
              </pre>
            )}
          </>
        )}
      </div>

      {/* Selected Result Modal */}
      {selectedResult && (
        <div className="modal-overlay" onClick={() => setSelectedResult(null)}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h3>{selectedResult.title || 'Scraping Result'}</h3>
              <button onClick={() => setSelectedResult(null)} className="modal-close">✕</button>
            </div>
            <div className="modal-body">
              <div className="modal-url">
                <strong>URL:</strong> <a href={selectedResult.url} target="_blank" rel="noopener noreferrer">{selectedResult.url}</a>
              </div>
              {selectedResult.success ? (
                <div className="modal-data">
                  <h4>Scraped Data:</h4>
                  <pre>{JSON.stringify(selectedResult.data?.data || {}, null, 2)}</pre>
                </div>
              ) : (
                <div className="modal-error">
                  <h4>Error:</h4>
                  <p>{selectedResult.error}</p>
                  {selectedResult.message && <p><strong>Details:</strong> {selectedResult.message}</p>}
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default BulkResultDisplay
