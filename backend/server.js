const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
require('dotenv').config();

const metascraperRoute = require('./scrapers/metascraper');
const crawleeRoute = require('./scrapers/crawlee');
const cheerioRoute = require('./scrapers/cheerio-scraper');
const stealthPlaywrightRoute = require('./scrapers/stealth-playwright');
const stealthPuppeteerRoute = require('./scrapers/stealth-puppeteer');

// Bulk scraping route
const bulkScraperRoute = require('./scrapers/bulk-scraper');

// Crawl/Link extraction routes
const crawlStealthPlaywrightRoute = require('./scrapers/crawl-stealth-playwright');
const crawlCrawleeRoute = require('./scrapers/crawl-crawlee');
const crawlCheerioRoute = require('./scrapers/crawl-cheerio');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(helmet());
app.use(cors());
app.use(express.json());

// Scraping Routes
app.use('/api/scrape/metascraper', metascraperRoute);
app.use('/api/scrape/crawlee', crawleeRoute);
app.use('/api/scrape/cheerio', cheerioRoute);
app.use('/api/scrape/stealth-playwright', stealthPlaywrightRoute);
app.use('/api/scrape/stealth-puppeteer', stealthPuppeteerRoute);

// Bulk scraping route
app.use('/api/scrape/bulk', bulkScraperRoute);

// Crawl/Link Extraction Routes
app.use('/api/crawl/stealth-playwright', crawlStealthPlaywrightRoute);
app.use('/api/crawl/crawlee', crawlCrawleeRoute);
app.use('/api/crawl/cheerio', crawlCheerioRoute);

// Health check
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', message: 'Web Scraper API is running' });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ 
    error: 'Something went wrong!', 
    message: err.message 
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Route not found' });
});

app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
  console.log(`Health check: http://localhost:${PORT}/api/health`);
});

module.exports = app;
