const express = require('express');
const axios = require('axios');
const router = express.Router();

// Helper function to scrape a single URL using the specified scraper
async function scrapeSingleUrl(url, scraper, options = {}) {
  try {
    const endpoint = `http://localhost:3001/api/scrape/${scraper}`;

    const payload = {
      url,
      elements: options.elements || null,
      customSelectors: options.customSelectors || [],
      waitTime: options.waitTime || 5000,
      retries: options.retries || 3
    };

    const response = await axios.post(endpoint, payload, {
      timeout: 120000 // 2 minute timeout per URL
    });

    return {
      success: true,
      data: response.data
    };

  } catch (error) {
    console.error(`Error scraping ${url}:`, error.message);

    return {
      success: false,
      error: error.response?.data?.error || 'Scraping failed',
      message: error.response?.data?.message || error.message
    };
  }
}

// Bulk scraping endpoint
router.post('/', async (req, res) => {
  try {
    const { 
      urls, 
      scraper = 'stealth-playwright', 
      elements = null,
      customSelectors = [],
      waitTime = 5000,
      retries = 3,
      maxConcurrent = 3,
      delayBetween = 1000
    } = req.body;

    // Validation
    if (!urls || !Array.isArray(urls) || urls.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'URLs array is required and must not be empty'
      });
    }

    if (urls.length > 50) {
      return res.status(400).json({
        success: false,
        error: 'Maximum 50 URLs allowed per bulk request'
      });
    }

    // Validate URLs
    const invalidUrls = [];
    urls.forEach((urlObj, index) => {
      const url = typeof urlObj === 'string' ? urlObj : urlObj.url;
      try {
        new URL(url);
      } catch (e) {
        invalidUrls.push({ index, url, error: 'Invalid URL format' });
      }
    });

    if (invalidUrls.length > 0) {
      return res.status(400).json({
        success: false,
        error: 'Invalid URLs found',
        invalidUrls
      });
    }

    console.log(`🚀 Starting bulk scraping for ${urls.length} URLs using ${scraper}`);

    const results = [];
    const startTime = Date.now();
    
    // Process URLs in batches to avoid overwhelming the system
    const batchSize = Math.min(maxConcurrent, 3);
    
    for (let i = 0; i < urls.length; i += batchSize) {
      const batch = urls.slice(i, i + batchSize);
      
      // Process batch concurrently
      const batchPromises = batch.map(async (urlObj, batchIndex) => {
        const actualIndex = i + batchIndex;
        const url = typeof urlObj === 'string' ? urlObj : urlObj.url;
        const urlTitle = typeof urlObj === 'object' ? urlObj.title : null;
        const urlDomain = typeof urlObj === 'object' ? urlObj.domain : null;
        
        console.log(`📄 Scraping ${actualIndex + 1}/${urls.length}: ${url}`);
        
        try {
          const result = await scrapeSingleUrl(url, scraper, {
            elements,
            customSelectors,
            waitTime,
            retries
          });
          
          return {
            index: actualIndex,
            url,
            title: urlTitle,
            domain: urlDomain,
            success: result.success,
            data: result.data || null,
            error: result.error || null,
            message: result.message || null,
            scrapedAt: new Date().toISOString()
          };
        } catch (error) {
          console.error(`❌ Error scraping ${url}:`, error.message);
          return {
            index: actualIndex,
            url,
            title: urlTitle,
            domain: urlDomain,
            success: false,
            data: null,
            error: 'Scraping failed',
            message: error.message,
            scrapedAt: new Date().toISOString()
          };
        }
      });
      
      // Wait for batch to complete
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
      
      // Add delay between batches (except for the last batch)
      if (i + batchSize < urls.length && delayBetween > 0) {
        await new Promise(resolve => setTimeout(resolve, delayBetween));
      }
    }

    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // Calculate statistics
    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;
    
    console.log(`✅ Bulk scraping completed: ${successful} successful, ${failed} failed in ${duration}ms`);

    // Return results
    res.json({
      success: true,
      data: {
        results,
        summary: {
          total: urls.length,
          successful,
          failed,
          duration,
          scraper,
          completedAt: new Date().toISOString()
        }
      }
    });

  } catch (error) {
    console.error('Bulk scraping error:', error);
    res.status(500).json({
      success: false,
      error: 'Bulk scraping failed',
      message: error.message
    });
  }
});

module.exports = router;
