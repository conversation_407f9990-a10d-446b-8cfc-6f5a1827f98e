const express = require('express');
const { chromium } = require('playwright');
const router = express.Router();

// Helper function to classify if a URL is likely a product page
function classifyProductPage(url, linkText, linkTitle, hasImage, pageContext = {}) {
  let score = 0;
  const urlLower = url.toLowerCase();
  const textLower = linkText.toLowerCase();
  const titleLower = linkTitle.toLowerCase();

  // URL pattern analysis for product pages
  const productUrlPatterns = [
    /\/[^\/]+\/\d+[-_]\d+\/?$/,  // /product-name/12345-6789 (Prophete pattern)
    /\/[^\/]+\/[a-z0-9-]+\.\d+\/?$/,  // /category/product.123
    /\/product[s]?\/[^\/]+\/?$/,      // /product/item-name
    /\/item[s]?\/[^\/]+\/?$/,         // /items/item-name
    /\/p\/[^\/]+\/?$/,                // /p/product-id
    /\/dp\/[^\/]+\/?$/,               // /dp/product-id (Amazon style)
    /\/[^\/]*\d{4,}[^\/]*\/?$/,       // URLs with 4+ digit product IDs
    /\/[^\/]+-\d+\/?$/,               // product-name-123
  ];

  // Check URL patterns
  for (const pattern of productUrlPatterns) {
    if (pattern.test(url)) {
      score += 30;
      break;
    }
  }

  // Navigation/category page patterns (negative score)
  const navigationPatterns = [
    /\/(category|categories|katalog|shop|browse|search|filter)\//i,
    /\/(inspirationen|inspiration|ideas|blog|news|about|contact|help|support)\//i,
    /\/(sale|angebote|deals|offers|promotion)\/?$/i,
    /\/(brand|marke|hersteller|manufacturer)\//i,
    /\/(service|warranty|garantie|shipping|versand)\//i,
    /\/[^\/]*\?(page|p|sort|order|filter|category)=/i,  // URLs with pagination/filtering
  ];

  for (const pattern of navigationPatterns) {
    if (pattern.test(url)) {
      score -= 20;
      break;
    }
  }

  // Content analysis
  const productIndicators = [
    'bike', 'fahrrad', 'e-bike', 'ebike', 'bicycle', 'rad',
    'model', 'modell', 'artikel', 'item', 'product', 'produkt'
  ];

  const navigationIndicators = [
    'alle', 'all', 'category', 'kategorie', 'mehr', 'more', 'view', 'show',
    'inspiration', 'ideas', 'blog', 'news', 'about', 'über'
  ];

  // Analyze link text
  for (const indicator of productIndicators) {
    if (textLower.includes(indicator)) {
      score += 5;
    }
  }

  for (const indicator of navigationIndicators) {
    if (textLower.includes(indicator)) {
      score -= 10;
    }
  }

  // Analyze title
  for (const indicator of productIndicators) {
    if (titleLower.includes(indicator)) {
      score += 3;
    }
  }

  // Image presence (products often have images)
  if (hasImage) {
    score += 10;
  }

  // URL structure analysis
  const pathSegments = new URL(url).pathname.split('/').filter(s => s);

  // Products often have specific URL structures
  if (pathSegments.length === 2 && pathSegments[1].match(/\d+[-_]\d+/)) {
    score += 25; // Strong indicator for Prophete-style URLs
  }

  // Too many path segments often indicate navigation
  if (pathSegments.length > 4) {
    score -= 5;
  }

  // Check for product-like naming patterns
  if (pathSegments.some(segment => {
    return segment.match(/^[a-z]+-\d+\.\d+$/) || // product-1.2
           segment.match(/^\d{5,}[-_]\d{4}$/) ||  // 55694-2101
           segment.match(/^[a-z]+-[a-z]+-\d+\.\d+$/); // word-word-1.2
  })) {
    score += 20;
  }

  return {
    score,
    isLikelyProduct: score > 15,
    confidence: Math.min(Math.max(score / 50, 0), 1) // Normalize to 0-1
  };
}

// Helper function to extract links from a page
async function extractLinksFromPage(url, options = {}) {
  const {
    maxLinks = 20,
    linkPattern = null,
    timeout = 60000,
    productPagesOnly = false,
    minProductScore = 15
  } = options;

  let browser;
  let context;
  let page;

  try {
    console.log(`Starting link extraction from: ${url}`);

    // Launch browser with stealth settings
    browser = await chromium.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor'
      ]
    });

    // Create context with stealth settings
    context = await browser.newContext({
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      viewport: { width: 1920, height: 1080 },
      locale: 'en-US',
      timezoneId: 'America/New_York',
      extraHTTPHeaders: {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
      }
    });

    page = await context.newPage();

    // Set additional stealth measures
    await page.addInitScript(() => {
      // Remove webdriver property
      delete navigator.__proto__.webdriver;
      
      // Mock plugins
      Object.defineProperty(navigator, 'plugins', {
        get: () => [1, 2, 3, 4, 5]
      });
      
      // Mock languages
      Object.defineProperty(navigator, 'languages', {
        get: () => ['en-US', 'en']
      });
    });

    // Navigate to the page
    console.log('Navigating to page...');
    await page.goto(url, { 
      waitUntil: 'networkidle',
      timeout: timeout 
    });

    // Wait for content to load
    await page.waitForTimeout(3000);

    // Extract all links from the page
    console.log('Extracting links...');
    const links = await page.evaluate(({ maxLinks, linkPattern, productPagesOnly, minProductScore }) => {
      const allLinks = Array.from(document.querySelectorAll('a[href]'));
      const extractedLinks = [];
      const seenUrls = new Set();

      // Helper function to classify product pages (duplicated in browser context)
      function classifyProductPage(url, linkText, linkTitle, hasImage) {
        let score = 0;
        const urlLower = url.toLowerCase();
        const textLower = linkText.toLowerCase();
        const titleLower = linkTitle.toLowerCase();

        // URL pattern analysis for product pages
        const productUrlPatterns = [
          /\/[^\/]+\/\d+[-_]\d+\/?$/,  // /product-name/12345-6789 (Prophete pattern)
          /\/[^\/]+\/[a-z0-9-]+\.\d+\/?$/,  // /category/product.123
          /\/product[s]?\/[^\/]+\/?$/,      // /product/item-name
          /\/item[s]?\/[^\/]+\/?$/,         // /items/item-name
          /\/p\/[^\/]+\/?$/,                // /p/product-id
          /\/dp\/[^\/]+\/?$/,               // /dp/product-id (Amazon style)
          /\/[^\/]*\d{4,}[^\/]*\/?$/,       // URLs with 4+ digit product IDs
          /\/[^\/]+-\d+\/?$/,               // product-name-123
        ];

        // Check URL patterns
        for (const pattern of productUrlPatterns) {
          if (pattern.test(url)) {
            score += 30;
            break;
          }
        }

        // Navigation/category page patterns (negative score)
        const navigationPatterns = [
          /\/(category|categories|katalog|shop|browse|search|filter)\//i,
          /\/(inspirationen|inspiration|ideas|blog|news|about|contact|help|support)\//i,
          /\/(sale|angebote|deals|offers|promotion)\/?$/i,
          /\/(brand|marke|hersteller|manufacturer)\//i,
          /\/(service|warranty|garantie|shipping|versand)\//i,
          /\/[^\/]*\?(page|p|sort|order|filter|category)=/i,  // URLs with pagination/filtering
        ];

        for (const pattern of navigationPatterns) {
          if (pattern.test(url)) {
            score -= 20;
            break;
          }
        }

        // Content analysis
        const productIndicators = [
          'bike', 'fahrrad', 'e-bike', 'ebike', 'bicycle', 'rad',
          'model', 'modell', 'artikel', 'item', 'product', 'produkt'
        ];

        const navigationIndicators = [
          'alle', 'all', 'category', 'kategorie', 'mehr', 'more', 'view', 'show',
          'inspiration', 'ideas', 'blog', 'news', 'about', 'über'
        ];

        // Analyze link text
        for (const indicator of productIndicators) {
          if (textLower.includes(indicator)) {
            score += 5;
          }
        }

        for (const indicator of navigationIndicators) {
          if (textLower.includes(indicator)) {
            score -= 10;
          }
        }

        // Analyze title
        for (const indicator of productIndicators) {
          if (titleLower.includes(indicator)) {
            score += 3;
          }
        }

        // Image presence (products often have images)
        if (hasImage) {
          score += 10;
        }

        // URL structure analysis
        const pathSegments = new URL(url).pathname.split('/').filter(s => s);

        // Products often have specific URL structures
        if (pathSegments.length === 2 && pathSegments[1].match(/\d+[-_]\d+/)) {
          score += 25; // Strong indicator for Prophete-style URLs
        }

        // Too many path segments often indicate navigation
        if (pathSegments.length > 4) {
          score -= 5;
        }

        // Check for product-like naming patterns
        if (pathSegments.some(segment => {
          return segment.match(/^[a-z]+-\d+\.\d+$/) || // product-1.2
                 segment.match(/^\d{5,}[-_]\d{4}$/) ||  // 55694-2101
                 segment.match(/^[a-z]+-[a-z]+-\d+\.\d+$/); // word-word-1.2
        })) {
          score += 20;
        }

        return {
          score,
          isLikelyProduct: score > 15,
          confidence: Math.min(Math.max(score / 50, 0), 1) // Normalize to 0-1
        };
      }

      for (const link of allLinks) {
        if (extractedLinks.length >= maxLinks) break;

        let href = link.href;

        // Skip invalid links
        if (!href || href.startsWith('javascript:') || href.startsWith('mailto:') || href.startsWith('tel:')) {
          continue;
        }

        // Convert relative URLs to absolute
        try {
          const url = new URL(href, window.location.href);
          href = url.href;
        } catch (e) {
          continue;
        }

        // Apply pattern filter if provided
        if (linkPattern && !href.includes(linkPattern)) {
          continue;
        }

        // Skip duplicates
        if (seenUrls.has(href)) {
          continue;
        }

        // Extract link information
        const linkText = link.textContent?.trim() || '';
        const title = link.title || '';
        const imageElement = link.querySelector('img');
        const image = imageElement ? imageElement.src : null;
        const hasImage = !!image;

        // Classify the link
        const classification = classifyProductPage(href, linkText, title, hasImage);

        // Filter by product classification if requested
        if (productPagesOnly && classification.score < minProductScore) {
          continue;
        }

        seenUrls.add(href);

        extractedLinks.push({
          url: href,
          text: linkText,
          title: title,
          image: image,
          domain: new URL(href).hostname,
          productScore: classification.score,
          isLikelyProduct: classification.isLikelyProduct,
          confidence: classification.confidence
        });
      }

      // Sort by product score (highest first) if product filtering is enabled
      if (productPagesOnly) {
        extractedLinks.sort((a, b) => b.productScore - a.productScore);
      }

      return extractedLinks;
    }, { maxLinks, linkPattern, productPagesOnly, minProductScore });

    console.log(`Extracted ${links.length} links`);

    // Get page metadata
    const pageInfo = await page.evaluate(() => {
      return {
        title: document.title,
        url: window.location.href,
        description: document.querySelector('meta[name="description"]')?.content || '',
        totalLinks: document.querySelectorAll('a[href]').length
      };
    });

    return {
      success: true,
      data: {
        pageInfo,
        links,
        extractedCount: links.length,
        totalLinksOnPage: pageInfo.totalLinks,
        extractedAt: new Date().toISOString()
      }
    };

  } catch (error) {
    console.error('Link extraction error:', error);
    return {
      success: false,
      error: 'Failed to extract links',
      message: error.message
    };
  } finally {
    try {
      if (page) await page.close();
      if (context) await context.close();
      if (browser) await browser.close();
    } catch (e) {
      console.error('Error closing browser:', e);
    }
  }
}

// POST endpoint for link extraction
router.post('/', async (req, res) => {
  try {
    const {
      url,
      maxLinks = 20,
      linkPattern,
      productPagesOnly = false,
      minProductScore = 15
    } = req.body;

    if (!url) {
      return res.status(400).json({
        success: false,
        error: 'URL is required'
      });
    }

    // Validate URL
    try {
      new URL(url);
    } catch (e) {
      return res.status(400).json({
        success: false,
        error: 'Invalid URL format'
      });
    }

    // Validate maxLinks
    const maxLinksNum = parseInt(maxLinks);
    if (isNaN(maxLinksNum) || maxLinksNum < 1 || maxLinksNum > 100) {
      return res.status(400).json({
        success: false,
        error: 'maxLinks must be a number between 1 and 100'
      });
    }

    // Validate minProductScore
    const minScoreNum = parseInt(minProductScore);
    if (isNaN(minScoreNum) || minScoreNum < 0 || minScoreNum > 100) {
      return res.status(400).json({
        success: false,
        error: 'minProductScore must be a number between 0 and 100'
      });
    }

    console.log(`Link extraction request: ${url} (max: ${maxLinksNum}, productOnly: ${productPagesOnly})`);

    const result = await extractLinksFromPage(url, {
      maxLinks: maxLinksNum,
      linkPattern: linkPattern?.trim() || null,
      productPagesOnly: Boolean(productPagesOnly),
      minProductScore: minScoreNum,
      timeout: 60000
    });

    res.json(result);

  } catch (error) {
    console.error('Route error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error.message
    });
  }
});

module.exports = router;
