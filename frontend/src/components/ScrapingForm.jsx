import { useState, useRef } from 'react'
import axios from 'axios'

const ScrapingForm = ({ onResult, onStart, onStop, isLoading }) => {
  const [url, setUrl] = useState('')
  const [scraper, setScraper] = useState('stealth-playwright')
  const [elements, setElements] = useState([])
  const [customSelectors, setCustomSelectors] = useState([])
  const [waitTime, setWaitTime] = useState(5000)
  const [retries, setRetries] = useState(3)
  const [customElements, setCustomElements] = useState('')
  const [showAdvanced, setShowAdvanced] = useState(false)
  const formRef = useRef(null)

  const availableElements = [
    // Basic Elements
    { id: 'title', label: 'Title', category: 'basic' },
    { id: 'description', label: 'Description', category: 'basic' },
    { id: 'images', label: 'Images', category: 'basic' },
    { id: 'author', label: 'Author', category: 'basic' },
    { id: 'website_name', label: 'Website Name', category: 'basic' },

    // E-commerce Elements
    { id: 'price', label: 'Price', category: 'ecommerce' },
    { id: 'old_price', label: 'Old Price', category: 'ecommerce' },
    { id: 'discount', label: 'Discount', category: 'ecommerce' },
    { id: 'brand', label: 'Brand', category: 'ecommerce' },
    { id: 'vendor', label: 'Vendor', category: 'ecommerce' },
    { id: 'category', label: 'Category', category: 'ecommerce' },
    { id: 'sku', label: 'SKU/Product ID', category: 'ecommerce' },
    { id: 'rating', label: 'Rating', category: 'ecommerce' },
    { id: 'reviews_count', label: 'Reviews Count', category: 'ecommerce' },
    { id: 'availability', label: 'Availability', category: 'ecommerce' },
    { id: 'shipping', label: 'Shipping Info', category: 'ecommerce' },
    { id: 'specifications', label: 'Specifications', category: 'ecommerce' },
    { id: 'variants', label: 'Product Variants', category: 'ecommerce' },

    // News Elements
    { id: 'headline', label: 'Headline', category: 'news' },
    { id: 'subtitle', label: 'Subtitle', category: 'news' },
    { id: 'byline', label: 'Byline', category: 'news' },
    { id: 'publish_date', label: 'Publish Date', category: 'news' },
    { id: 'update_date', label: 'Update Date', category: 'news' },
    { id: 'tags', label: 'Tags', category: 'news' },
    { id: 'keywords', label: 'Keywords', category: 'news' },
    { id: 'summary', label: 'Summary', category: 'news' },
    { id: 'content', label: 'Full Content', category: 'news' },
    { id: 'source', label: 'Source', category: 'news' },
    { id: 'location', label: 'Location', category: 'news' },

    // Social & Engagement
    { id: 'social_shares', label: 'Social Shares', category: 'social' },
    { id: 'comments_count', label: 'Comments Count', category: 'social' },
    { id: 'likes', label: 'Likes', category: 'social' },
    { id: 'views', label: 'Views', category: 'social' },

    // Technical Elements
    { id: 'meta_title', label: 'Meta Title', category: 'technical' },
    { id: 'meta_description', label: 'Meta Description', category: 'technical' },
    { id: 'canonical_url', label: 'Canonical URL', category: 'technical' },
    { id: 'language', label: 'Language', category: 'technical' },
    { id: 'breadcrumbs', label: 'Breadcrumbs', category: 'technical' }
  ]

  const scraperOptions = [
    {
      id: 'metascraper',
      label: 'Metascraper',
      description: 'Spezialisiert auf Metadaten-Extraktion',
      details: 'Extrahiert automatisch Titel, Beschreibungen, Bilder und andere Metadaten aus HTML-Meta-Tags. Ideal für schnelle Übersichten und SEO-Daten.'
    },
    {
      id: 'crawlee',
      label: 'Crawlee',
      description: 'JavaScript-gerenderte Inhalte',
      details: 'Modernes Web-Scraping-Framework mit Playwright-Integration. Perfekt für Single-Page-Applications und dynamische Inhalte, die JavaScript benötigen.'
    },
    {
      id: 'cheerio',
      label: 'Cheerio',
      description: 'Schnelles HTML-Parsing',
      details: 'Server-seitiges jQuery für Node.js. Sehr schnell und effizient für statische HTML-Inhalte. Unterstützt benutzerdefinierte CSS-Selektoren.'
    },
    {
      id: 'stealth-playwright',
      label: 'Stealth Playwright',
      description: '🛡️ Anti-Detection Browser-Automatisierung',
      details: 'Erweiterte Browser-Automatisierung mit Anti-Detection-Features. Umgeht Bot-Schutz-Systeme durch realistische Browser-Simulation und Stealth-Techniken.'
    },
    {
      id: 'stealth-puppeteer',
      label: 'Stealth Puppeteer',
      description: '🥷 Erweiterte Cloudflare-Umgehung',
      details: 'Hochentwickelte Anti-Bot-Detection mit Puppeteer-Extra und Stealth-Plugin. Simuliert menschliches Verhalten, umgeht Cloudflare-Challenges und andere Bot-Schutz-Systeme.'
    }
  ]

  const handleElementChange = (elementId) => {
    setElements(prev => 
      prev.includes(elementId) 
        ? prev.filter(id => id !== elementId)
        : [...prev, elementId]
    )
  }

  const addCustomSelector = () => {
    setCustomSelectors(prev => [...prev, { name: '', selector: '', attribute: '' }])
  }

  const updateCustomSelector = (index, field, value) => {
    setCustomSelectors(prev => 
      prev.map((item, i) => 
        i === index ? { ...item, [field]: value } : item
      )
    )
  }

  const removeCustomSelector = (index) => {
    setCustomSelectors(prev => prev.filter((_, i) => i !== index))
  }

  const toggleAdvancedOptions = () => {
    setShowAdvanced(!showAdvanced)
    // Scroll to top when opening options
    if (!showAdvanced) {
      setTimeout(() => {
        window.scrollTo({
          top: 0,
          behavior: 'smooth'
        })
      }, 100)
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()

    if (!url) {
      alert('Please enter a URL')
      return
    }

    // Create AbortController for this request
    const controller = new AbortController()
    onStart(controller)

    try {
      const endpoint = `http://localhost:3001/api/scrape/${scraper}`

      // Combine selected elements with custom elements
      const allElements = [...elements]
      if (customElements.trim()) {
        const customElementsList = customElements.split(',').map(el => el.trim()).filter(el => el)
        allElements.push(...customElementsList)
      }

      const payload = {
        url,
        elements: allElements.length > 0 ? allElements : null,
        customSelectors: customSelectors.filter(cs => cs.name && cs.selector),
        waitTime: parseInt(waitTime),
        retries: parseInt(retries)
      }

      const response = await axios.post(endpoint, payload, {
        signal: controller.signal,
        timeout: 60000 // 60 second timeout
      })
      onResult(response.data)
    } catch (error) {
      console.error('Scraping error:', error)

      // Check if the error was due to cancellation
      if (error.name === 'CanceledError' || error.code === 'ERR_CANCELED') {
        // Don't call onResult here, let the parent handle the cancellation
        return
      }

      onResult({
        success: false,
        error: error.response?.data?.error || 'Failed to scrape URL',
        message: error.response?.data?.message || error.message
      })
    }
  }

  return (
    <div className="scraping-form" ref={formRef}>
      <div className="url-section">
        <label className="url-label">URL</label>
        <div className="url-input-container">
          <input
            type="url"
            className="url-input"
            value={url}
            onChange={(e) => setUrl(e.target.value)}
            placeholder="https://www.decathlon.de/p/mp/cube/refurbished-cube-stereo-120-hpc-slt-sehr-gut/_/R-p-7c94d446-a391-47ef-b360-630f86a39263?mc=7c94d446-a391-47ef-b360-630f86a39263_c1&c=SCHWARZ"
            disabled={isLoading}
          />
          <div className="url-actions">
            <button type="button" className="btn-secondary" disabled={isLoading}>
              <span className="btn-icon">📋</span>
              Get Code
            </button>
            <button
              type="button"
              className="btn-primary"
              onClick={handleSubmit}
              disabled={isLoading || !url.trim()}
            >
              <span className="btn-icon">▶️</span>
              Run
            </button>
            <button
              type="button"
              className="btn-secondary btn-stop"
              disabled={!isLoading}
              onClick={onStop}
            >
              <span className="btn-icon">⏹️</span>
              Stop
            </button>
          </div>
        </div>
      </div>

      <div className="options-section">
        <button
          type="button"
          className="options-toggle"
          onClick={toggleAdvancedOptions}
        >
          Options {showAdvanced ? '▲' : '▼'}
        </button>
      </div>

      {showAdvanced && (
        <div className="advanced-options">
          <form onSubmit={handleSubmit}>
            <div className="form-group">
              <label htmlFor="url">URL to scrape:</label>
              <input
                type="url"
                id="url"
                value={url}
                onChange={(e) => setUrl(e.target.value)}
                placeholder="https://example.com"
                required
                disabled={isLoading}
              />
            </div>

        <div className="form-group">
          <label>Scraping Library:</label>
          <div className="scraper-options">
            {scraperOptions.map(option => (
              <div
                key={option.id}
                className={`radio-option ${scraper === option.id ? 'selected' : ''}`}
                onClick={() => !isLoading && setScraper(option.id)}
              >
                <input
                  type="radio"
                  name="scraper"
                  value={option.id}
                  checked={scraper === option.id}
                  onChange={(e) => setScraper(e.target.value)}
                  disabled={isLoading}
                />
                <div className="radio-indicator"></div>
                <div className="radio-content">
                  <div className="radio-header">
                    <strong>{option.label}</strong>
                    <span className="radio-description">{option.description}</span>
                  </div>
                  <div className="radio-details">
                    {option.details}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="form-group">
          <label>Elements to extract:</label>
          <div className="element-categories">
            {['basic', 'ecommerce', 'news', 'social', 'technical'].map(category => {
              const categoryElements = availableElements.filter(el => el.category === category);
              const categoryLabels = {
                basic: '📄 Basic Elements',
                ecommerce: '🛒 E-commerce',
                news: '📰 News & Articles',
                social: '👥 Social & Engagement',
                technical: '⚙️ Technical'
              };

              return (
                <div key={category} className="element-category">
                  <h4 className="category-title">{categoryLabels[category]}</h4>
                  <div className="element-checkboxes">
                    {categoryElements.map(element => (
                      <label key={element.id} className="checkbox-option">
                        <input
                          type="checkbox"
                          checked={elements.includes(element.id)}
                          onChange={() => handleElementChange(element.id)}
                          disabled={isLoading}
                        />
                        {element.label}
                      </label>
                    ))}
                  </div>
                </div>
              );
            })}
          </div>
          <small>Select specific elements to extract. Leave empty to extract all available non-technical elements automatically.</small>
        </div>

        <div className="form-group">
          <label htmlFor="customElements">Custom Elements:</label>
          <input
            type="text"
            id="customElements"
            value={customElements}
            onChange={(e) => setCustomElements(e.target.value)}
            placeholder="e.g. brand, category, rating, discount"
            disabled={isLoading}
          />
          <small>Enter additional elements to extract, separated by commas</small>
        </div>

        {scraper === 'cheerio' && (
          <div className="form-group">
            <label>Custom CSS Selectors:</label>
            {customSelectors.map((selector, index) => (
              <div key={index} className="custom-selector">
                <input
                  type="text"
                  placeholder="Field name"
                  value={selector.name}
                  onChange={(e) => updateCustomSelector(index, 'name', e.target.value)}
                  disabled={isLoading}
                />
                <input
                  type="text"
                  placeholder="CSS selector"
                  value={selector.selector}
                  onChange={(e) => updateCustomSelector(index, 'selector', e.target.value)}
                  disabled={isLoading}
                />
                <input
                  type="text"
                  placeholder="Attribute (optional)"
                  value={selector.attribute}
                  onChange={(e) => updateCustomSelector(index, 'attribute', e.target.value)}
                  disabled={isLoading}
                />
                <button 
                  type="button" 
                  onClick={() => removeCustomSelector(index)}
                  disabled={isLoading}
                  className="remove-btn"
                >
                  ✕
                </button>
              </div>
            ))}
            <button 
              type="button" 
              onClick={addCustomSelector}
              disabled={isLoading}
              className="add-selector-btn"
            >
              + Add Custom Selector
            </button>
          </div>
        )}

        {(scraper === 'stealth-playwright' || scraper === 'stealth-puppeteer') && (
          <div className="form-group">
            <label>Advanced Settings:</label>
            <div className="advanced-settings">
              <div className="setting-item">
                <label htmlFor="waitTime">Wait Time (ms):</label>
                <input
                  type="number"
                  id="waitTime"
                  value={waitTime}
                  onChange={(e) => setWaitTime(e.target.value)}
                  min="1000"
                  max="30000"
                  step="1000"
                  disabled={isLoading}
                />
                <small>Time to wait for dynamic content to load</small>
              </div>
              <div className="setting-item">
                <label htmlFor="retries">Retries:</label>
                <input
                  type="number"
                  id="retries"
                  value={retries}
                  onChange={(e) => setRetries(e.target.value)}
                  min="1"
                  max="5"
                  disabled={isLoading}
                />
                <small>Number of retry attempts if scraping fails</small>
              </div>
            </div>
          </div>
        )}

            <button type="submit" disabled={isLoading} className="submit-btn">
              {isLoading ? 'Scraping...' : 'Start Scraping'}
            </button>
          </form>
        </div>
      )}

      {/* Status Section */}
      <div className="status-section">
        <div className="status-info">
          <span className="status-text">
            {isLoading ? (
              <>
                <span className="status-indicator streaming">🟠</span>
                Streaming scraping results...
              </>
            ) : (
              <>
                <span className="status-indicator">📊</span>
                Total pages scraped: 1
              </>
            )}
          </span>
          <div className="status-actions">
            <button className="status-btn" disabled={!isLoading}>
              📥 Download Results
            </button>
            <button className="status-btn error" disabled>
              ⚠️ Report Scraping Issue
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ScrapingForm
