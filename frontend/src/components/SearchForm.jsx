import { useState, useRef } from 'react'
import axios from 'axios'

const SearchForm = ({
  onResult,
  onStart,
  onStop,
  isLoading,
  onElementsChange,
  onCustomElementsChange,
  elements: propElements,
  customElements: propCustomElements
}) => {
  const [url, setUrl] = useState('')
  const [scraper, setScraper] = useState('stealth-playwright')
  const [maxLinks, setMaxLinks] = useState(20)
  const [linkPattern, setLinkPattern] = useState('')
  const [elements, setElements] = useState(propElements || ['title', 'description', 'images', 'price'])
  const [customElements, setCustomElements] = useState(propCustomElements || '')
  const [showAdvanced, setShowAdvanced] = useState(false)
  const formRef = useRef(null)

  const availableElements = [
    // Basic Elements
    { id: 'title', label: 'Title', category: 'basic' },
    { id: 'description', label: 'Description', category: 'basic' },
    { id: 'images', label: 'Images', category: 'basic' },
    { id: 'author', label: 'Author', category: 'basic' },
    { id: 'website_name', label: 'Website Name', category: 'basic' },

    // E-commerce Elements
    { id: 'price', label: 'Price', category: 'ecommerce' },
    { id: 'old_price', label: 'Old Price', category: 'ecommerce' },
    { id: 'discount', label: 'Discount', category: 'ecommerce' },
    { id: 'brand', label: 'Brand', category: 'ecommerce' },
    { id: 'vendor', label: 'Vendor', category: 'ecommerce' },
    { id: 'category', label: 'Category', category: 'ecommerce' },
    { id: 'sku', label: 'SKU/Product ID', category: 'ecommerce' },
    { id: 'rating', label: 'Rating', category: 'ecommerce' },
    { id: 'reviews_count', label: 'Reviews Count', category: 'ecommerce' },
    { id: 'availability', label: 'Availability', category: 'ecommerce' },
    { id: 'shipping', label: 'Shipping Info', category: 'ecommerce' },
    { id: 'specifications', label: 'Specifications', category: 'ecommerce' },
    { id: 'variants', label: 'Product Variants', category: 'ecommerce' },

    // News Elements
    { id: 'headline', label: 'Headline', category: 'news' },
    { id: 'subtitle', label: 'Subtitle', category: 'news' },
    { id: 'byline', label: 'Byline', category: 'news' },
    { id: 'publish_date', label: 'Publish Date', category: 'news' },
    { id: 'update_date', label: 'Update Date', category: 'news' },
    { id: 'tags', label: 'Tags', category: 'news' },
    { id: 'keywords', label: 'Keywords', category: 'news' },
    { id: 'summary', label: 'Summary', category: 'news' },
    { id: 'content', label: 'Full Content', category: 'news' },
    { id: 'source', label: 'Source', category: 'news' },
    { id: 'location', label: 'Location', category: 'news' },

    // Social & Engagement
    { id: 'social_shares', label: 'Social Shares', category: 'social' },
    { id: 'comments_count', label: 'Comments Count', category: 'social' },
    { id: 'likes', label: 'Likes', category: 'social' },
    { id: 'views', label: 'Views', category: 'social' },

    // Technical Elements
    { id: 'meta_title', label: 'Meta Title', category: 'technical' },
    { id: 'meta_description', label: 'Meta Description', category: 'technical' },
    { id: 'canonical_url', label: 'Canonical URL', category: 'technical' },
    { id: 'language', label: 'Language', category: 'technical' },
    { id: 'breadcrumbs', label: 'Breadcrumbs', category: 'technical' }
  ]

  const scraperOptions = [
    {
      id: 'stealth-playwright',
      label: 'Stealth Playwright',
      description: 'Best for modern e-commerce sites',
      details: 'Uses stealth techniques to bypass bot detection. Ideal for sites like Decathlon, Amazon, etc.'
    },
    {
      id: 'crawlee',
      label: 'Crawlee',
      description: 'JavaScript-rendered content',
      details: 'Modern web scraping with Playwright integration. Perfect for dynamic content.'
    },
    {
      id: 'cheerio',
      label: 'Cheerio',
      description: 'Fast HTML parsing',
      details: 'Server-side jQuery for Node.js. Very fast for static HTML content.'
    }
  ]

  const handleElementChange = (elementId) => {
    const newElements = elements.includes(elementId)
      ? elements.filter(id => id !== elementId)
      : [...elements, elementId]

    setElements(newElements)
    if (onElementsChange) {
      onElementsChange(newElements)
    }
  }

  const handleCustomElementsChange = (value) => {
    setCustomElements(value)
    if (onCustomElementsChange) {
      onCustomElementsChange(value)
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!url) {
      alert('Please enter a URL')
      return
    }

    // Create AbortController for this request
    const controller = new AbortController()
    onStart(controller)

    try {
      const endpoint = `http://localhost:3001/api/search/${scraper}`

      const payload = {
        url,
        maxLinks: parseInt(maxLinks),
        linkPattern: linkPattern.trim() || null
      }

      const response = await axios.post(endpoint, payload, {
        signal: controller.signal,
        timeout: 120000 // 2 minute timeout for link extraction
      })
      onResult(response.data)
    } catch (error) {
      console.error('Link extraction error:', error)
      
      // Check if the error was due to cancellation
      if (error.name === 'CanceledError' || error.code === 'ERR_CANCELED') {
        return
      }
      
      onResult({
        success: false,
        error: error.response?.data?.error || 'Failed to extract links',
        message: error.response?.data?.message || error.message
      })
    }
  }

  const toggleAdvancedOptions = () => {
    setShowAdvanced(!showAdvanced)
    // Scroll to top when opening options
    if (!showAdvanced && formRef.current) {
      setTimeout(() => {
        formRef.current.scrollTo({
          top: 0,
          behavior: 'smooth'
        })
      }, 100)
    }
  }

  return (
    <div className="scraping-form" ref={formRef}>
      <div className="url-section">
        <label className="url-label">Category/Listing Page URL</label>
        <div className="url-input-container">
          <input
            type="url"
            className="url-input"
            value={url}
            onChange={(e) => setUrl(e.target.value)}
            placeholder="https://www.decathlon.de/alle-sportarten-a-z/fahrrad-welt/e-mountainbikes"
            disabled={isLoading}
          />
          <div className="url-actions">
            <button
              type="button"
              className="btn-primary"
              onClick={handleSubmit}
              disabled={isLoading || !url.trim()}
            >
              <span className="btn-icon">🔍</span>
              Extract Links
            </button>
            <button 
              type="button" 
              className="btn-secondary btn-stop" 
              disabled={!isLoading}
              onClick={onStop}
            >
              <span className="btn-icon">⏹️</span>
              Stop
            </button>
          </div>
        </div>
      </div>

      <div className="options-section">
        <button
          type="button"
          className="options-toggle"
          onClick={toggleAdvancedOptions}
        >
          Options {showAdvanced ? '▲' : '▼'}
        </button>
      </div>

      {showAdvanced && (
        <div className="advanced-options">
          <form onSubmit={handleSubmit}>
            <div className="form-group">
              <label htmlFor="search-url">Category/Listing Page URL:</label>
              <input
                type="url"
                id="search-url"
                value={url}
                onChange={(e) => setUrl(e.target.value)}
                placeholder="https://example.com/category/products"
                required
                disabled={isLoading}
              />
              <small>Enter a URL that contains multiple product/listing links</small>
            </div>

            <div className="form-group">
              <label>Scraper Engine:</label>
              <div className="scraper-options">
                {scraperOptions.map(option => (
                  <div
                    key={option.id}
                    className={`radio-option ${scraper === option.id ? 'selected' : ''}`}
                    onClick={() => !isLoading && setScraper(option.id)}
                  >
                    <input
                      type="radio"
                      id={option.id}
                      name="scraper"
                      value={option.id}
                      checked={scraper === option.id}
                      onChange={(e) => setScraper(e.target.value)}
                      disabled={isLoading}
                    />
                    <div className="radio-indicator"></div>
                    <div className="radio-content">
                      <div className="radio-header">
                        <strong>{option.label}</strong>
                        <div className="radio-description">{option.description}</div>
                      </div>
                      <div className="radio-details">{option.details}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="form-group">
              <label htmlFor="maxLinks">Maximum Links to Extract:</label>
              <input
                type="number"
                id="maxLinks"
                value={maxLinks}
                onChange={(e) => setMaxLinks(e.target.value)}
                min="1"
                max="100"
                disabled={isLoading}
              />
              <small>Limit the number of product links to extract (1-100)</small>
            </div>

            <div className="form-group">
              <label htmlFor="linkPattern">Link Pattern Filter (optional):</label>
              <input
                type="text"
                id="linkPattern"
                value={linkPattern}
                onChange={(e) => setLinkPattern(e.target.value)}
                placeholder="e.g., /product/, /p/, /item/"
                disabled={isLoading}
              />
              <small>Only extract links containing this pattern (leave empty for all links)</small>
            </div>

            <div className="form-group">
              <label>Elements to extract for bulk scraping:</label>
              <div className="element-categories">
                {['basic', 'ecommerce', 'news', 'social', 'technical'].map(category => {
                  const categoryElements = availableElements.filter(el => el.category === category);
                  const categoryLabels = {
                    basic: '📄 Basic Elements',
                    ecommerce: '🛒 E-commerce',
                    news: '📰 News & Articles',
                    social: '👥 Social & Engagement',
                    technical: '⚙️ Technical'
                  };

                  return (
                    <div key={category} className="element-category">
                      <h4 className="category-title">{categoryLabels[category]}</h4>
                      <div className="element-checkboxes">
                        {categoryElements.map(element => (
                          <label key={element.id} className="checkbox-option">
                            <input
                              type="checkbox"
                              checked={elements.includes(element.id)}
                              onChange={() => handleElementChange(element.id)}
                              disabled={isLoading}
                            />
                            {element.label}
                          </label>
                        ))}
                      </div>
                    </div>
                  );
                })}
              </div>
              <small>Select elements to extract when bulk scraping the found links. These will be used for all scraped pages.</small>
            </div>

            <div className="form-group">
              <label htmlFor="customElements">Custom Elements for bulk scraping:</label>
              <input
                type="text"
                id="customElements"
                value={customElements}
                onChange={(e) => handleCustomElementsChange(e.target.value)}
                placeholder="e.g. brand, category, rating, discount"
                disabled={isLoading}
              />
              <small>Enter additional elements to extract during bulk scraping, separated by commas</small>
            </div>

            <button type="submit" disabled={isLoading} className="submit-btn">
              {isLoading ? 'Extracting Links...' : 'Extract Product Links'}
            </button>
          </form>
        </div>
      )}

      {/* Status Section */}
      <div className="status-section">
        <div className="status-info">
          <span className="status-text">
            {isLoading ? (
              <>
                <span className="status-indicator streaming">🟠</span>
                Extracting product links...
              </>
            ) : (
              <>
                <span className="status-indicator">🔍</span>
                Ready to extract links from category pages
              </>
            )}
          </span>
          <div className="status-actions">
            <button className="status-btn" disabled={!isLoading}>
              📥 Download Results
            </button>
            <button className="status-btn error" disabled>
              ⚠️ Report Issue
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default SearchForm
