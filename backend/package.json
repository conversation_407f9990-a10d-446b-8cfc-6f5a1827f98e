{"name": "web-scraper-backend", "version": "1.0.0", "description": "Backend for web scraper application", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"axios": "^1.9.0", "cheerio": "^1.0.0-rc.12", "cors": "^2.8.5", "crawlee": "^3.5.8", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "metascraper": "^5.34.6", "metascraper-author": "^5.34.6", "metascraper-clearbit": "^5.34.6", "metascraper-date": "^5.34.6", "metascraper-description": "^5.34.6", "metascraper-image": "^5.34.6", "metascraper-logo": "^5.34.6", "metascraper-publisher": "^5.34.6", "metascraper-title": "^5.34.6", "metascraper-url": "^5.34.6", "playwright": "^1.40.0", "puppeteer": "^24.9.0", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "user-agents": "^1.1.0"}, "devDependencies": {"nodemon": "^3.0.2"}}