import { useState } from 'react'

const CrawlResultDisplay = ({ result, onScrapeLink }) => {
  const [viewMode, setViewMode] = useState('formatted')
  const [selectedLinks, setSelectedLinks] = useState(new Set())

  const downloadJson = () => {
    const dataStr = JSON.stringify(result, null, 2)
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr)
    const exportFileDefaultName = `extracted-links-${new Date().toISOString().split('T')[0]}.json`

    const linkElement = document.createElement('a')
    linkElement.setAttribute('href', dataUri)
    linkElement.setAttribute('download', exportFileDefaultName)
    linkElement.click()
  }

  const copyToClipboard = () => {
    navigator.clipboard.writeText(JSON.stringify(result, null, 2))
      .then(() => alert('<PERSON><PERSON><PERSON> copied to clipboard!'))
      .catch(err => console.error('Failed to copy: ', err))
  }

  const toggleLinkSelection = (index) => {
    const newSelected = new Set(selectedLinks)
    if (newSelected.has(index)) {
      newSelected.delete(index)
    } else {
      newSelected.add(index)
    }
    setSelectedLinks(newSelected)
  }

  const selectAllLinks = () => {
    if (result.data?.links) {
      setSelectedLinks(new Set(result.data.links.map((_, index) => index)))
    }
  }

  const deselectAllLinks = () => {
    setSelectedLinks(new Set())
  }

  const scrapeSelectedLinks = () => {
    if (selectedLinks.size === 0) {
      alert('Please select at least one link to scrape')
      return
    }

    const linksToScrape = result.data.links.filter((_, index) => selectedLinks.has(index))
    onScrapeLink(linksToScrape)
  }

  if (!result) return null

  return (
    <div className="result-display">
      <div className="result-header">
        <h2>🔍 Link Extraction Results</h2>
        <div className="result-actions">
          <div className="view-toggle">
            <button 
              className={viewMode === 'formatted' ? 'active' : ''}
              onClick={() => setViewMode('formatted')}
            >
              Formatted
            </button>
            <button 
              className={viewMode === 'raw' ? 'active' : ''}
              onClick={() => setViewMode('raw')}
            >
              Raw JSON
            </button>
          </div>
          <button onClick={copyToClipboard} className="action-btn">
            📋 Copy
          </button>
          <button onClick={downloadJson} className="action-btn">
            💾 Download
          </button>
        </div>
      </div>

      <div className="result-content">
        {result.success === false ? (
          <div className="error-result">
            <h3>❌ Link Extraction Failed</h3>
            <p><strong>Error:</strong> {result.error}</p>
            {result.message && <p><strong>Details:</strong> {result.message}</p>}
          </div>
        ) : (
          <>
            <div className="result-meta">
              <div className="meta-item">
                <strong>Source URL:</strong> {result.data?.pageInfo?.url || 'N/A'}
              </div>
              <div className="meta-item">
                <strong>Page Title:</strong> {result.data?.pageInfo?.title || 'N/A'}
              </div>
              <div className="meta-item">
                <strong>Links Found:</strong> {result.data?.extractedCount || 0} / {result.data?.totalLinksOnPage || 0}
              </div>
              <div className="meta-item">
                <strong>Extracted At:</strong> {result.data?.extractedAt ? new Date(result.data.extractedAt).toLocaleString() : 'N/A'}
              </div>
            </div>

            {viewMode === 'formatted' ? (
              <div className="formatted-result">
                {result.data?.links && result.data.links.length > 0 ? (
                  <div className="links-section">
                    <div className="links-header">
                      <h3>🔗 Extracted Links ({result.data.links.length})</h3>
                      <div className="links-actions">
                        <button onClick={selectAllLinks} className="action-btn">
                          ✅ Select All
                        </button>
                        <button onClick={deselectAllLinks} className="action-btn">
                          ❌ Deselect All
                        </button>
                        <button 
                          onClick={scrapeSelectedLinks} 
                          className="btn-primary"
                          disabled={selectedLinks.size === 0}
                        >
                          🚀 Scrape Selected ({selectedLinks.size})
                        </button>
                      </div>
                    </div>

                    <div className="links-grid">
                      {result.data.links.map((link, index) => (
                        <div 
                          key={index} 
                          className={`link-card ${selectedLinks.has(index) ? 'selected' : ''}`}
                        >
                          <div className="link-header">
                            <input
                              type="checkbox"
                              checked={selectedLinks.has(index)}
                              onChange={() => toggleLinkSelection(index)}
                              className="link-checkbox"
                            />
                            <div className="link-info">
                              <div className="link-domain">{link.domain}</div>
                              {link.title && <div className="link-title">{link.title}</div>}
                            </div>
                          </div>

                          {link.image && (
                            <div className="link-image">
                              <img 
                                src={link.image} 
                                alt="Link preview" 
                                onError={(e) => e.target.style.display = 'none'}
                              />
                            </div>
                          )}

                          <div className="link-content">
                            {link.text && (
                              <div className="link-text">
                                {link.text.length > 100 ? link.text.substring(0, 100) + '...' : link.text}
                              </div>
                            )}
                            <div className="link-url">
                              <a 
                                href={link.url} 
                                target="_blank" 
                                rel="noopener noreferrer"
                                onClick={(e) => e.stopPropagation()}
                              >
                                {link.url.length > 60 ? link.url.substring(0, 60) + '...' : link.url}
                              </a>
                            </div>
                          </div>

                          <div className="link-actions">
                            <button 
                              onClick={() => onScrapeLink([link])}
                              className="scrape-single-btn"
                            >
                              🔍 Scrape This
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="no-links">
                    <h3>🔍 No Links Found</h3>
                    <p>No links were extracted from the provided URL. This could be because:</p>
                    <ul>
                      <li>The page doesn't contain any links matching your criteria</li>
                      <li>The page requires JavaScript to load content</li>
                      <li>The link pattern filter was too restrictive</li>
                      <li>The page structure is not compatible with the selected scraper</li>
                    </ul>
                  </div>
                )}
              </div>
            ) : (
              <pre className="raw-json">
                {JSON.stringify(result, null, 2)}
              </pre>
            )}
          </>
        )}
      </div>
    </div>
  )
}

export default CrawlResultDisplay
