const express = require('express');
const axios = require('axios');
const cheerio = require('cheerio');
const { URL } = require('url');
const router = express.Router();

// Helper function to extract links using Cheerio
async function extractLinksWithCheerio(url, options = {}) {
  const {
    maxLinks = 20,
    linkPattern = null,
    timeout = 30000
  } = options;

  try {
    console.log(`Starting Cheerio link extraction from: ${url}`);

    // Fetch the page
    const response = await axios.get(url, {
      timeout: timeout,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
      },
      maxRedirects: 5
    });

    // Load HTML into Cheerio
    const $ = cheerio.load(response.data);

    // Get page info
    const pageInfo = {
      title: $('title').text().trim() || '',
      url: url,
      description: $('meta[name="description"]').attr('content') || '',
      totalLinks: $('a[href]').length
    };

    console.log(`Page loaded: ${pageInfo.title}`);
    console.log(`Total links found: ${pageInfo.totalLinks}`);

    // Extract links
    const extractedLinks = [];
    const seenUrls = new Set();

    $('a[href]').each((index, element) => {
      if (extractedLinks.length >= maxLinks) return false; // Break the loop

      const $link = $(element);
      let href = $link.attr('href');

      // Skip invalid links
      if (!href || href.startsWith('javascript:') || href.startsWith('mailto:') || href.startsWith('tel:')) {
        return;
      }

      // Convert relative URLs to absolute
      try {
        const absoluteUrl = new URL(href, url);
        href = absoluteUrl.href;
      } catch (e) {
        return;
      }

      // Apply pattern filter if provided
      if (linkPattern && !href.includes(linkPattern)) {
        return;
      }

      // Skip duplicates
      if (seenUrls.has(href)) {
        return;
      }

      seenUrls.add(href);

      // Extract link information
      const linkText = $link.text().trim() || '';
      const title = $link.attr('title') || '';
      const $image = $link.find('img').first();
      const image = $image.length > 0 ? $image.attr('src') : null;

      // Convert relative image URLs to absolute
      let absoluteImage = null;
      if (image) {
        try {
          absoluteImage = new URL(image, url).href;
        } catch (e) {
          absoluteImage = image;
        }
      }

      extractedLinks.push({
        url: href,
        text: linkText,
        title: title,
        image: absoluteImage,
        domain: new URL(href).hostname
      });
    });

    console.log(`Extracted ${extractedLinks.length} links`);

    return {
      success: true,
      data: {
        pageInfo,
        links: extractedLinks,
        extractedCount: extractedLinks.length,
        totalLinksOnPage: pageInfo.totalLinks,
        extractedAt: new Date().toISOString()
      }
    };

  } catch (error) {
    console.error('Cheerio link extraction error:', error);
    
    let errorMessage = error.message;
    if (error.code === 'ENOTFOUND') {
      errorMessage = 'Website not found or unreachable';
    } else if (error.code === 'ECONNREFUSED') {
      errorMessage = 'Connection refused by the server';
    } else if (error.code === 'ETIMEDOUT') {
      errorMessage = 'Request timed out';
    } else if (error.response?.status) {
      errorMessage = `HTTP ${error.response.status}: ${error.response.statusText}`;
    }

    return {
      success: false,
      error: 'Failed to extract links',
      message: errorMessage
    };
  }
}

// POST endpoint for link extraction
router.post('/', async (req, res) => {
  try {
    const { url, maxLinks = 20, linkPattern } = req.body;

    if (!url) {
      return res.status(400).json({
        success: false,
        error: 'URL is required'
      });
    }

    // Validate URL
    try {
      new URL(url);
    } catch (e) {
      return res.status(400).json({
        success: false,
        error: 'Invalid URL format'
      });
    }

    // Validate maxLinks
    const maxLinksNum = parseInt(maxLinks);
    if (isNaN(maxLinksNum) || maxLinksNum < 1 || maxLinksNum > 100) {
      return res.status(400).json({
        success: false,
        error: 'maxLinks must be a number between 1 and 100'
      });
    }

    console.log(`Cheerio link extraction request: ${url} (max: ${maxLinksNum})`);

    const result = await extractLinksWithCheerio(url, {
      maxLinks: maxLinksNum,
      linkPattern: linkPattern?.trim() || null,
      timeout: 30000
    });

    res.json(result);

  } catch (error) {
    console.error('Cheerio route error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error.message
    });
  }
});

module.exports = router;
