const express = require('express');
const { chromium } = require('playwright');
const UserAgent = require('user-agents');

const router = express.Router();

// Realistic browser configurations
const getBrowserConfig = () => {
  const userAgent = new UserAgent();
  
  return {
    headless: true, // Verwende headless für bessere Performance
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage',
      '--disable-accelerated-2d-canvas',
      '--no-first-run',
      '--no-zygote',
      '--disable-gpu',
      '--disable-features=VizDisplayCompositor',
      '--disable-background-timer-throttling',
      '--disable-backgrounding-occluded-windows',
      '--disable-renderer-backgrounding',
      '--disable-field-trial-config',
      '--disable-back-forward-cache',
      '--disable-backgrounding-occluded-windows',
      '--disable-features=TranslateUI',
      '--disable-ipc-flooding-protection',
      '--window-size=1920,1080',
      '--start-maximized'
    ],
    viewport: { width: 1920, height: 1080 },
    userAgent: userAgent.toString(),
    locale: 'de-DE',
    timezoneId: 'Europe/Berlin'
  };
};

// Simulate human behavior
const humanDelay = (min = 1000, max = 3000) => {
  return new Promise(resolve => {
    const delay = Math.floor(Math.random() * (max - min + 1)) + min;
    setTimeout(resolve, delay);
  });
};

// Random mouse movements
const simulateHumanBehavior = async (page) => {
  // Random mouse movements
  for (let i = 0; i < 3; i++) {
    await page.mouse.move(
      Math.random() * 1920,
      Math.random() * 1080,
      { steps: 10 }
    );
    await humanDelay(100, 500);
  }
  
  // Random scroll
  await page.evaluate(() => {
    window.scrollTo(0, Math.random() * 500);
  });
  
  await humanDelay(500, 1500);
};

// Enhanced page stealth setup
const setupPageStealth = async (page) => {
  // Override webdriver detection
  await page.addInitScript(() => {
    Object.defineProperty(navigator, 'webdriver', {
      get: () => undefined,
    });
    
    // Remove automation indicators
    delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
    delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
    delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
    
    // Override permissions
    const originalQuery = window.navigator.permissions.query;
    window.navigator.permissions.query = (parameters) => (
      parameters.name === 'notifications' ?
        Promise.resolve({ state: Notification.permission }) :
        originalQuery(parameters)
    );
    
    // Override plugins
    Object.defineProperty(navigator, 'plugins', {
      get: () => [1, 2, 3, 4, 5],
    });
    
    // Override languages
    Object.defineProperty(navigator, 'languages', {
      get: () => ['de-DE', 'de', 'en-US', 'en'],
    });
  });
  
  // Set realistic headers
  await page.setExtraHTTPHeaders({
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'Accept-Encoding': 'gzip, deflate, br',
    'Accept-Language': 'de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7',
    'Cache-Control': 'max-age=0',
    'sec-ch-ua': '"Google Chrome";v="119", "Chromium";v="119", "Not?A_Brand";v="24"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"macOS"',
    'Sec-Fetch-Dest': 'document',
    'Sec-Fetch-Mode': 'navigate',
    'Sec-Fetch-Site': 'none',
    'Sec-Fetch-User': '?1',
    'Upgrade-Insecure-Requests': '1'
  });
};

router.post('/', async (req, res) => {
  let browser = null;
  let page = null;
  
  try {
    const { url, elements, waitTime = 5000, retries = 3 } = req.body;

    if (!url) {
      return res.status(400).json({ error: 'URL is required' });
    }

    // Define all available non-technical elements
    const allNonTechnicalElements = [
      // Basic Elements
      'title', 'description', 'images', 'author', 'website_name',
      // E-commerce Elements
      'price', 'old_price', 'currency', 'brand', 'category', 'rating', 'reviews_count',
      'availability', 'discount', 'vendor', 'sku', 'product_id',
      // News Elements
      'headline', 'publish_date', 'tags', 'content',
      // Social Elements
      'social_shares', 'comments_count', 'likes_count'
    ];

    // If no elements specified, use all non-technical elements
    const elementsToExtract = (elements && elements.length > 0) ? elements : allNonTechnicalElements;

    console.log(`🚀 Starting stealth scraping for: ${url}`);
    
    // Launch browser with stealth configuration
    const config = getBrowserConfig();
    browser = await chromium.launch(config);
    
    const context = await browser.newContext({
      viewport: config.viewport,
      userAgent: config.userAgent,
      locale: config.locale,
      timezoneId: config.timezoneId,
      permissions: ['geolocation'],
      geolocation: { latitude: 52.520008, longitude: 13.404954 }, // Berlin
    });
    
    page = await context.newPage();
    
    // Setup stealth measures
    await setupPageStealth(page);
    
    // Navigate with retry logic
    let attempt = 0;
    let navigationSuccess = false;
    
    while (attempt < retries && !navigationSuccess) {
      try {
        console.log(`📡 Attempt ${attempt + 1} to load page...`);
        
        // Add random delay before navigation
        await humanDelay(1000, 3000);
        
        // Navigate to page
        const response = await page.goto(url, {
          waitUntil: 'domcontentloaded',
          timeout: 30000
        });
        
        if (response && response.status() === 200) {
          navigationSuccess = true;
          console.log('✅ Page loaded successfully');
        } else {
          console.log(`⚠️ Received status: ${response ? response.status() : 'unknown'}`);
        }
        
      } catch (error) {
        console.log(`❌ Navigation attempt ${attempt + 1} failed:`, error.message);
        attempt++;
        
        if (attempt < retries) {
          await humanDelay(2000, 5000); // Wait before retry
        }
      }
    }
    
    if (!navigationSuccess) {
      throw new Error('Failed to load page after all retries');
    }
    
    // Wait for page to fully load and simulate human behavior
    await humanDelay(2000, 4000);
    await simulateHumanBehavior(page);
    
    // Check if we're on a Cloudflare challenge page
    const isChallengePage = await page.evaluate(() => {
      return document.title.includes('Just a moment') || 
             document.body.textContent.includes('Checking your browser') ||
             document.querySelector('#challenge-error-text') !== null;
    });
    
    if (isChallengePage) {
      console.log('🛡️ Cloudflare challenge detected, waiting...');
      
      // Wait for challenge to complete (up to 30 seconds)
      try {
        await page.waitForFunction(() => {
          return !document.title.includes('Just a moment') && 
                 !document.body.textContent.includes('Checking your browser');
        }, { timeout: 30000 });
        
        console.log('✅ Challenge completed');
        await humanDelay(2000, 4000);
        
      } catch (error) {
        console.log('⚠️ Challenge timeout, proceeding anyway...');
      }
    }
    
    // Additional wait time for dynamic content
    await page.waitForTimeout(waitTime);
    
    // Extract data based on requested elements
    const scrapedData = await page.evaluate((requestedElements) => {
      const result = {};
      
      // Helper functions
      const getTextContent = (selector) => {
        const element = document.querySelector(selector);
        return element ? element.textContent.trim() : null;
      };
      
      const getAttribute = (selector, attribute) => {
        const element = document.querySelector(selector);
        return element ? element.getAttribute(attribute) : null;
      };
      
      const getAllElements = (selector, attribute = null) => {
        const elements = document.querySelectorAll(selector);
        return Array.from(elements).map(el => 
          attribute ? el.getAttribute(attribute) : el.textContent.trim()
        ).filter(item => item);
      };
      
      // Only extract elements that are specifically requested
      if (requestedElements && requestedElements.includes('title')) {
        result.title = getTextContent('title') ||
                     getTextContent('h1') ||
                     getTextContent('[data-testid="title"]') ||
                     getTextContent('.title') ||
                     getTextContent('.product-title');
      }

      // Extract description
      if (requestedElements && requestedElements.includes('description')) {
        result.description = getAttribute('meta[name="description"]', 'content') ||
                           getAttribute('meta[property="og:description"]', 'content') ||
                           getTextContent('.description') ||
                           getTextContent('[data-testid="description"]') ||
                           getTextContent('.product-description');
      }

      // Extract images
      if (requestedElements && (requestedElements.includes('images') || requestedElements.includes('image'))) {
        const images = [];

        // Get og:image
        const ogImage = getAttribute('meta[property="og:image"]', 'content');
        if (ogImage) images.push(ogImage);

        // Enhanced image selectors for e-commerce sites
        const imageSelectors = [
          '.product-image img', '.item-image img', '.gallery img',
          '[data-testid*="image"] img', '.product-photo img',
          '.product-gallery img', '.main-image img', '.hero-image img',
          '.slider img', '.carousel img', '.thumbnail img',
          '[class*="image"] img', '[class*="photo"] img',
          '[id*="image"] img', '[id*="photo"] img'
        ];

        // Get product images with specific selectors
        for (const selector of imageSelectors) {
          const imgs = getAllElements(selector, 'src');
          images.push(...imgs);
        }

        // Get all img src attributes with improved filtering
        const allImages = getAllElements('img', 'src');
        images.push(...allImages.filter(src => {
          if (!src || src.length < 10) return false;
          if (src.startsWith('data:')) return false;

          // Include images that are likely product images
          const includePatterns = [
            'product', 'item', 'image', 'photo', 'gallery', 'main',
            'hero', 'slider', 'carousel', 'thumbnail', 'zoom',
            'large', 'medium', 'small', 'detail', 'view'
          ];

          // Exclude common non-product images
          const excludePatterns = [
            'icon', 'logo', 'banner', 'ad', 'advertisement',
            'social', 'facebook', 'twitter', 'instagram',
            'pixel', 'tracking', 'analytics', 'sprite'
          ];

          const srcLower = src.toLowerCase();
          const hasIncludePattern = includePatterns.some(pattern => srcLower.includes(pattern));
          const hasExcludePattern = excludePatterns.some(pattern => srcLower.includes(pattern));

          return hasIncludePattern && !hasExcludePattern;
        }));

        // Filter and clean images
        let filteredImages = [...new Set(images)]
          .filter(src => src && !src.includes('data:') && src.length > 10);

        // If we have very few images, be less restrictive
        if (filteredImages.length < 3) {
          const allImagesUnfiltered = getAllElements('img', 'src');
          const lessRestrictiveImages = allImagesUnfiltered.filter(src => {
            if (!src || src.length < 10) return false;
            if (src.startsWith('data:')) return false;

            // Exclude only the most obvious non-product images
            const excludePatterns = [
              'icon', 'logo', 'banner', 'ad', 'advertisement',
              'pixel', 'tracking', 'analytics', 'sprite', 'favicon'
            ];

            const srcLower = src.toLowerCase();
            return !excludePatterns.some(pattern => srcLower.includes(pattern));
          });

          filteredImages.push(...lessRestrictiveImages);
          filteredImages = [...new Set(filteredImages)];
        }

        result.images = filteredImages.slice(0, 10);
      }
      
      // Enhanced price extraction with intelligent parsing
      if (requestedElements && requestedElements.includes('price')) {
        const priceSelectors = [
          '.price', '[data-testid="price"]', '.cost', '.amount',
          '[class*="price"]', '[id*="price"]', '.currency',
          '.product-price', '.price-current', '.price-now',
          '[data-price]', '.price-value', '.sale-price'
        ];

        for (const selector of priceSelectors) {
          const priceElement = document.querySelector(selector);
          if (priceElement) {
            const priceText = priceElement.textContent.trim();
            if (priceText && /[\d.,]+/.test(priceText)) {
              // Parse multiple prices from the same element
              const prices = priceText.match(/[\d.,]+\s*€/g);
              if (prices && prices.length >= 2) {
                // First price is usually the current price, second is old price
                result.price = prices[0].trim();
                result.old_price = prices[1].trim();
              } else {
                result.price = priceText;
              }
              break;
            }
          }
        }

        // Try data attributes
        if (!result.price) {
          const priceElement = document.querySelector('[data-price]');
          if (priceElement) {
            result.price = priceElement.getAttribute('data-price');
          }
        }
      }

      // Extract old prices (only if not already found in price parsing)
      if (requestedElements && requestedElements.includes('old_price') && !result.old_price) {
        const oldPriceSelectors = [
          '.old-price', '.original-price', '.was-price', '.regular-price',
          '.price-old', '.price-before', '.crossed-price', '.strikethrough-price',
          '[data-testid="old-price"]', '[class*="old-price"]', '[class*="original-price"]',
          '[class*="was-price"]', 'del', 's', '.line-through', '.price-strike'
        ];

        for (const selector of oldPriceSelectors) {
          const oldPriceElement = document.querySelector(selector);
          if (oldPriceElement) {
            const oldPriceText = oldPriceElement.textContent.trim();
            if (oldPriceText && /[\d.,]+/.test(oldPriceText)) {
              result.old_price = oldPriceText;
              break;
            }
          }
        }

        // Try data attributes for old price
        if (!result.old_price) {
          const oldPriceElement = document.querySelector('[data-old-price]');
          if (oldPriceElement) {
            result.old_price = oldPriceElement.getAttribute('data-old-price');
          }
        }
      }
      
      // Extract author/brand
      if (requestedElements && requestedElements.includes('author')) {
        result.author = getAttribute('meta[name="author"]', 'content') ||
                      getTextContent('.author') ||
                      getTextContent('[data-testid="author"]') ||
                      getTextContent('.brand') ||
                      getTextContent('.manufacturer');
      }

      // Extract website name
      if (requestedElements && requestedElements.includes('website_name')) {
        result.website_name = getAttribute('meta[property="og:site_name"]', 'content') ||
                            getAttribute('meta[name="application-name"]', 'content') ||
                            getTextContent('.site-name') ||
                            getTextContent('.logo-text') ||
                            getTextContent('[data-testid="site-name"]') ||
                            // Fallback to domain name from URL
                            (typeof window !== 'undefined' ? window.location.hostname : '');
      }

      // E-commerce Elements
      if (requestedElements && requestedElements.includes('brand')) {
        result.brand = getTextContent('.brand') ||
                     getTextContent('.manufacturer') ||
                     getTextContent('[data-testid="brand"]') ||
                     getTextContent('[class*="brand"]') ||
                     getAttribute('meta[property="product:brand"]', 'content');
      }

      if (requestedElements && requestedElements.includes('vendor')) {
        result.vendor = getTextContent('.vendor') ||
                      getTextContent('.seller') ||
                      getTextContent('.merchant') ||
                      getTextContent('[data-testid="vendor"]') ||
                      getTextContent('[class*="vendor"]') ||
                      getTextContent('[class*="seller"]') ||
                      getAttribute('meta[property="product:retailer"]', 'content');
      }

      if (requestedElements && requestedElements.includes('category')) {
        result.category = getTextContent('.category') ||
                        getTextContent('.breadcrumb') ||
                        getTextContent('[data-testid="category"]') ||
                        getTextContent('.product-category');
      }

      if (requestedElements && requestedElements.includes('sku')) {
        result.sku = getTextContent('.sku') ||
                   getTextContent('.product-id') ||
                   getTextContent('[data-testid="sku"]') ||
                   getAttribute('[data-product-id]', 'data-product-id');
      }

      if (requestedElements && requestedElements.includes('rating')) {
        result.rating = getTextContent('.rating') ||
                      getTextContent('.stars') ||
                      getTextContent('[data-testid="rating"]') ||
                      getAttribute('[data-rating]', 'data-rating');
      }

      if (requestedElements && requestedElements.includes('reviews_count')) {
        result.reviews_count = getTextContent('.reviews-count') ||
                             getTextContent('.review-count') ||
                             getTextContent('[data-testid="reviews"]');
      }

      if (requestedElements && requestedElements.includes('availability')) {
        result.availability = getTextContent('.availability') ||
                            getTextContent('.stock') ||
                            getTextContent('[data-testid="availability"]') ||
                            getTextContent('.in-stock');
      }

      if (requestedElements && requestedElements.includes('discount')) {
        result.discount = getTextContent('.discount') ||
                        getTextContent('.save') ||
                        getTextContent('[data-testid="discount"]') ||
                        getTextContent('.percentage-off');
      }

      // News Elements
      if (requestedElements && requestedElements.includes('headline')) {
        result.headline = getTextContent('h1') ||
                        getTextContent('.headline') ||
                        getTextContent('[data-testid="headline"]') ||
                        getTextContent('.article-title');
      }

      if (requestedElements && requestedElements.includes('subtitle')) {
        result.subtitle = getTextContent('.subtitle') ||
                        getTextContent('.subheading') ||
                        getTextContent('h2') ||
                        getTextContent('[data-testid="subtitle"]');
      }

      if (requestedElements && requestedElements.includes('byline')) {
        result.byline = getTextContent('.byline') ||
                      getTextContent('.author-line') ||
                      getTextContent('[data-testid="byline"]');
      }

      if (requestedElements && requestedElements.includes('publish_date')) {
        result.publish_date = getAttribute('meta[property="article:published_time"]', 'content') ||
                            getAttribute('time[datetime]', 'datetime') ||
                            getTextContent('time') ||
                            getTextContent('.publish-date') ||
                            getTextContent('[data-testid="date"]');
      }

      if (requestedElements && requestedElements.includes('tags')) {
        const tagElements = getAllElements('.tag, .tags a, [data-testid="tag"]');
        result.tags = tagElements.length > 0 ? tagElements : null;
      }

      if (requestedElements && requestedElements.includes('content')) {
        result.content = getTextContent('.article-content') ||
                       getTextContent('.content') ||
                       getTextContent('[data-testid="content"]') ||
                       getTextContent('article');
      }

      // Technical Elements
      if (requestedElements && requestedElements.includes('meta_title')) {
        result.meta_title = getAttribute('meta[property="og:title"]', 'content') ||
                          getTextContent('title');
      }

      if (requestedElements && requestedElements.includes('meta_description')) {
        result.meta_description = getAttribute('meta[name="description"]', 'content') ||
                                 getAttribute('meta[property="og:description"]', 'content');
      }

      if (requestedElements && requestedElements.includes('canonical_url')) {
        result.canonical_url = getAttribute('link[rel="canonical"]', 'href');
      }

      if (requestedElements && requestedElements.includes('breadcrumbs')) {
        const breadcrumbElements = getAllElements('.breadcrumb a, [data-testid="breadcrumb"] a');
        result.breadcrumbs = breadcrumbElements.length > 0 ? breadcrumbElements : null;
      }

      return result;
    }, elementsToExtract);
    
    // Take screenshot for debugging
    await page.screenshot({ path: 'debug-screenshot.png', fullPage: false });
    
    const result = {
      url: url,
      scraper: 'stealth-playwright',
      timestamp: new Date().toISOString(),
      data: scrapedData,
      success: true,
      challengeDetected: isChallengePage
    };
    
    console.log('✅ Scraping completed successfully');
    res.json(result);
    
  } catch (error) {
    console.error('❌ Stealth Playwright error:', error);
    res.status(500).json({
      error: 'Failed to scrape URL with Stealth Playwright',
      message: error.message,
      success: false
    });
  } finally {
    try {
      if (page) await page.close();
    } catch (error) {
      console.log('⚠️ Error closing page:', error.message);
    }

    try {
      if (browser) await browser.close();
    } catch (error) {
      console.log('⚠️ Error closing browser:', error.message);
    }
  }
});

module.exports = router;
